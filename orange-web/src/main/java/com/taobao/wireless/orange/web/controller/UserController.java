package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.User;
import com.taobao.wireless.orange.manager.UserManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "用户相关接口")
@RestController
@RequestMapping("/api/users")  // 复数形式
@Slf4j
public class UserController {

    @Autowired
    private UserManager userManager;

    @ApiOperation("根据工号批量查询用户信息")
    @GetMapping("/")
    public Result<Map<String, User>> query(@RequestParam("workNoList") String workNoList) {
        if (StringUtils.isNotBlank(workNoList)) {
            Map<String, User> userMap = userManager.queryUserByEmpIds(List.of(workNoList.split(",")));
            return Result.success(userMap);
        }
        return Result.success(null);
    }
}
