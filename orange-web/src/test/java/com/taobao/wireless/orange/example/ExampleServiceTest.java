package com.taobao.wireless.orange.example;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.service.ReleaseOrderService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 示例测试类 - 展示如何使用BaseTransactionalTest
 *
 * 这个示例展示了：
 * 1. 如何继承BaseTransactionalTest
 * 2. 如何使用基类提供的工具方法
 * 3. 如何编写简洁的测试代码
 * 4. 如何利用自动事务回滚
 *
 * <AUTHOR> Team
 */
public class ExampleServiceTest extends BaseTransactionalTest {

    @Autowired
    private ReleaseOrderService releaseOrderService;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    /**
     * 示例测试方法 - 展示基本用法
     */
    @Test
    public void testCreateReleaseOrder() {
        // 使用基类提供的工具方法生成测试数据
        String testReleaseVersion = generateTestVersion("release");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();

        logTestInfo("开始测试创建发布单: " + testReleaseVersion);

        // 创建测试发布单
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 验证创建结果
        OReleaseOrderDO savedOrder = releaseOrderDAO.getByReleaseVersion(testReleaseVersion);
        Assert.assertNotNull("发布单应该被成功创建", savedOrder);
        Assert.assertEquals("发布版本应该匹配", testReleaseVersion, savedOrder.getReleaseVersion());
        Assert.assertEquals("应用Key应该匹配", testAppKey, savedOrder.getAppKey());

        logTestInfo("发布单创建测试完成");
    }

    /**
     * 示例测试方法 - 展示异步操作测试
     */
    @Test
    public void testAsyncOperation() {
        String testReleaseVersion = generateTestVersion("async");

        logTestInfo("开始测试异步操作");

        // 模拟启动异步操作
        startAsyncOperation(testReleaseVersion);

        // 使用基类提供的等待方法
        boolean completed = waitForAsyncOperation(() -> {
            // 检查异步操作是否完成
            return checkAsyncOperationStatus(testReleaseVersion);
        });

        Assert.assertTrue("异步操作应该在指定时间内完成", completed);
        logTestInfo("异步操作测试完成");
    }

    /**
     * 示例测试方法 - 展示性能测试
     */
    @Test
    public void testPerformance() {
        String testReleaseVersion = generateTestVersion("perf");

        // 使用基类提供的性能测试方法
        assertPerformance("查询发布单", () -> {
            // 执行需要测试性能的操作
            releaseOrderService.getOperations(testReleaseVersion);
        }, 1000); // 最大允许1秒

        logTestInfo("性能测试完成");
    }

    /**
     * 示例测试方法 - 展示工作流测试
     */
    @Test
    public void testReleaseWorkflow() {
        String testReleaseVersion = generateTestVersion("workflow");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();

        // 使用基类提供的工作流验证方法
        verifyWorkflow("发布流程",
            () -> {
                // 步骤1：创建发布单
                OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
                releaseOrderDAO.save(releaseOrder);
                logTestInfo("步骤1完成：创建发布单");
            },
            () -> {
                // 步骤2：验证发布单
                OReleaseOrderDO order = releaseOrderDAO.getByReleaseVersion(testReleaseVersion);
                Assert.assertNotNull("发布单应该存在", order);
                logTestInfo("步骤2完成：验证发布单");
            },
            () -> {
                // 步骤3：获取操作记录
                Result result = releaseOrderService.getOperations(testReleaseVersion);
                assertSuccess(result, "获取操作记录应该成功");
                logTestInfo("步骤3完成：获取操作记录");
            }
        );
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建测试发布单
     */
    private OReleaseOrderDO createTestReleaseOrder(String releaseVersion, String appKey, String namespaceId) {
        OReleaseOrderDO releaseOrder = new OReleaseOrderDO();
        releaseOrder.setReleaseVersion(releaseVersion);
        releaseOrder.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrder.setBizId(namespaceId);
        releaseOrder.setAppKey(appKey);
        releaseOrder.setNamespaceId(namespaceId);
        releaseOrder.setReleaseType(ReleaseType.PUBLISH);
        releaseOrder.setStatus(ReleaseOrderStatus.INIT);
        releaseOrder.setDescription("测试发布单 - " + releaseVersion);
        releaseOrder.setGmtCreate(getCurrentTime());
        releaseOrder.setGmtModified(getCurrentTime());
        return releaseOrder;
    }

    /**
     * 模拟启动异步操作
     */
    private void startAsyncOperation(String releaseVersion) {
        logTestInfo("启动异步操作: " + releaseVersion);
        // 这里可以调用实际的异步服务
    }

    /**
     * 检查异步操作状态
     */
    private boolean checkAsyncOperationStatus(String releaseVersion) {
        // 这里可以检查实际的异步操作状态
        // 为了示例，我们模拟操作已完成
        return true;
    }

    /**
     * 自定义设置方法示例
     */
    @Override
    protected void customSetUp() throws Exception {
        super.customSetUp();
        // 可以在这里进行测试类特有的初始化
        logTestInfo("ExampleServiceTest 初始化完成");
    }

    /**
     * 自定义清理方法示例
     */
    @Override
    protected void cleanupTestData() {
        super.cleanupTestData();
        // 可以在这里进行测试类特有的清理工作
        logTestInfo("ExampleServiceTest 清理完成");
    }
}
