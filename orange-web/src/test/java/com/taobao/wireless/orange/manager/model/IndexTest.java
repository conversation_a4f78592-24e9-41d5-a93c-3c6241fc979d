package com.taobao.wireless.orange.manager.model;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.pandora.boot.test.junit4.DelegateTo;
import com.taobao.pandora.boot.test.junit4.PandoraBootRunner;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.external.OssService;
import com.taobao.wireless.orange.web.Application;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PandoraBootRunner.class)
@DelegateTo(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {Application.class})
public class IndexTest {
    @Autowired
    private OssService ossService;

    @Value("${orange.oss.bucketName}")
    public String bucketName;

    @Test
    public void testSerializeAndDeserialize() throws Exception {
        String resourceId = "bc2b4fda35ad4a95a84c4d692111f4d7.bin";
        byte[] bytes = ossService.readData(bucketName, resourceId);

        try {
            MessageOrBuilder proto = IndexProto.parseFrom(bytes);
            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);
            Assert.assertEquals("{\n" +
                    "  \"schemaVersion\": \"1.0\",\n" +
                    "  \"cdn\": \"o-config-testing.oss-rg-china-mainland.aliyuncs.com\",\n" +
                    "  \"appKey\": \"21380790\",\n" +
                    "  \"version\": \"2025052317031560400\",\n" +
                    "  \"baseVersion\": \"0\",\n" +
                    "  \"offlineNamespaces\": [],\n" +
                    "  \"strategy\": \"FULL\",\n" +
                    "  \"namespaces\": [{\n" +
                    "    \"name\": \"测试命名空间-c4b89e82\",\n" +
                    "    \"changeVersion\": \"2025052317031560400\",\n" +
                    "    \"version\": \"0\",\n" +
                    "    \"release\": {\n" +
                    "      \"resourceId\": \"0d7d142086e6451a9e793daacaecbfd7.bin\",\n" +
                    "      \"resourceMd5\": \"cee6b5b5aef9e3ca41998e8d7878c6c5\"\n" +
                    "    }\n" +
                    "  }, {\n" +
                    "    \"name\": \"阮萤测试\",\n" +
                    "    \"changeVersion\": \"2025051517180151100\",\n" +
                    "    \"version\": \"0\",\n" +
                    "    \"release\": {\n" +
                    "      \"resourceId\": \"05d57d393380492b84894d8d6e559ed9.bin\",\n" +
                    "      \"resourceMd5\": \"87321d199252efc22b37fb34308e06ef\"\n" +
                    "    }\n" +
                    "  }, {\n" +
                    "    \"name\": \"orange_test_ios\",\n" +
                    "    \"changeVersion\": \"2025051514552212732\",\n" +
                    "    \"version\": \"0\",\n" +
                    "    \"release\": {\n" +
                    "      \"resourceId\": \"24de9a001c804002bb0a545a204bde29.bin\",\n" +
                    "      \"resourceMd5\": \"1ea926aa2cb06913fb7321d5763ebbd9\"\n" +
                    "    },\n" +
                    "    \"gray\": {\n" +
                    "      \"orders\": [{\n" +
                    "        \"version\": \"2025051514552200332\",\n" +
                    "        \"percent\": 10\n" +
                    "      }],\n" +
                    "      \"resourceId\": \"5799362cf8174f44bcccfe95b485fbf5.bin\",\n" +
                    "      \"resourceMd5\": \"0c16ebceb0f9b7da530ce6c27b97da79\"\n" +
                    "    }\n" +
                    "  }]\n" +
                    "}", jsonStr);
        } catch (Exception e) {
            Assert.assertNull(e);
        }
    }
}
