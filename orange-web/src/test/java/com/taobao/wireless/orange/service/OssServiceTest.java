package com.taobao.wireless.orange.service;

import com.taobao.pandora.boot.test.junit4.DelegateTo;
import com.taobao.pandora.boot.test.junit4.PandoraBootRunner;
import com.taobao.wireless.orange.external.OssService;
import com.taobao.wireless.orange.web.Application;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;


@RunWith(PandoraBootRunner.class)
@DelegateTo(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {Application.class})
@Transactional
public class OssServiceTest {
    @Autowired
    private OssService ossService;

    @Test
    @Ignore
    public void putObj() {
        this.ossService.uploadData("test", "o-config-testing", "test.json");
    }
}
