package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.service.model.NamespaceCreateDTO;
import com.taobao.wireless.orange.service.model.NamespaceDTO;
import com.taobao.wireless.orange.service.model.NamespaceQueryDTO;
import com.taobao.wireless.orange.service.model.NamespaceUpdateDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import com.taobao.wireless.orange.web.Application;
import com.taobao.pandora.boot.test.junit4.DelegateTo;
import com.taobao.pandora.boot.test.junit4.PandoraBootRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;

import static com.taobao.wireless.orange.common.exception.ExceptionEnum.NAMESPACE_NOT_EXIST;
import static org.junit.Assert.*;

@RunWith(PandoraBootRunner.class)
@DelegateTo(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {Application.class})
@Transactional
public class NamespaceServiceTest {

    @Autowired
    private NamespaceService namespaceService;

    private NamespaceDTO testNamespaceDTO;
    private String testNamespaceId = "test-namespace-id";
    private String testNamespaceName = "test-namespace-name";
    private String testAppKey = "test-app-key";

    @Before
    public void setUp() {
        testNamespaceDTO = new NamespaceDTO();
        testNamespaceDTO.setNamespaceId(testNamespaceId);
        testNamespaceDTO.setName(testNamespaceName);
        testNamespaceDTO.setBizType(NamespaceBizType.MODULE);
        testNamespaceDTO.setBizId("test-biz-id");
        testNamespaceDTO.setNamespaceId(testNamespaceId);
        testNamespaceDTO.setOwners(Collections.singletonList("149016"));
        testNamespaceDTO.setTesters(Collections.singletonList("149020"));
        testNamespaceDTO.setAppKey(testAppKey);
    }

    /**
     * 测试用例：测试创建命名空间功能 - 命名空间已存在
     * 场景：当尝试创建一个已存在名称的命名空间时，应该抛出异常
     */
    @Test
    @Transactional
    public void testCreate_WhenNamespaceExists_ShouldThrowException() {
        NamespaceCreateDTO createDTO = BeanUtil.createFromProperties(testNamespaceDTO, NamespaceCreateDTO.class);
        Result<String> result = namespaceService.create(createDTO);
        Assert.assertFalse(result.getSuccess());
        assertEquals(ExceptionEnum.NAMESPACE_NAME_DUPLICATE.getCode(), result.getCode());
    }

    /**
     * 测试用例：测试创建命名空间功能 - 成功场景
     * 场景：创建一个全新的命名空间，应该成功并返回ID
     */
    @Test
    @Transactional
    public void testCreate_WithNewNamespace_ShouldSucceed() {
        NamespaceCreateDTO newNamespace = new NamespaceCreateDTO();
        String uniqueName = "test-namespace-" + UUID.randomUUID().toString().substring(0, 8);
        newNamespace.setName(uniqueName);
        newNamespace.setBizType(NamespaceBizType.MODULE);
        newNamespace.setBizId("new-biz-id");
        newNamespace.setOwners(Collections.singletonList("149016"));
        newNamespace.setTesters(Collections.singletonList("149017"));
        newNamespace.setAppKey("new-app-key");

        Result<String> result = namespaceService.create(newNamespace);
        assertTrue("创建的命名空间ID不为空", StringUtils.isNotBlank(result.getData()));
    }

    /**
     * 测试用例：测试创建命名空间功能 - 缺少必要字段
     * 场景：创建命名空间时缺少必要字段，应该抛出异常
     */
    @Test
    @Transactional
    public void testCreate_WithMissingRequiredFields_ShouldThrowException() {
        NamespaceCreateDTO incompleteNamespace = new NamespaceCreateDTO();
        incompleteNamespace.setName("incomplete-namespace");
        // 故意不设置其他必要字段

        Result<String> result = namespaceService.create(incompleteNamespace);
        assertFalse("创建命名空间时缺少必要字段，应该抛出异常", result.getSuccess());
        assertEquals(ExceptionEnum.PARAM_INVALID.getCode(), result.getCode());
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 基本场景
     * 场景：正常情况下，传入条件、页码和大小，返回分页结果
     */
    @Test
    @Transactional
    public void testQuery_ShouldReturnPageResult() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey(testAppKey);
        condition.setName(testNamespaceName);
        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, 1, 10);
        assertTrue(result.getTotal() > 0);
        assertEquals(testNamespaceId, result.getData().get(0).getNamespaceId());
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 多条件查询
     * 场景：使用多个条件组合查询，应返回符合条件的结果
     */
    @Test
    @Transactional
    public void testQuery_WithMultipleConditions_ShouldReturnFilteredResults() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey(testAppKey);

        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, 1, 10);
        assertTrue("应该有结果返回", result.getTotal() > 0);

        for (NamespaceDTO ns : result.getData()) {
            assertEquals("结果应该匹配查询的AppKey", testAppKey, ns.getAppKey());
            assertEquals("结果应该匹配查询的业务类型", NamespaceBizType.MODULE, ns.getBizType());
        }
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 无匹配结果
     * 场景：查询条件没有匹配的记录，应返回空结果集
     */
    @Test
    @Transactional
    public void testQuery_WithNoMatchingConditions_ShouldReturnEmptyResults() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey("non-existent-app-key");
        condition.setName("non-existent-name");

        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, 1, 10);
        assertTrue("没有匹配记录时结果列表应为空", result.getData().isEmpty());
    }

    /**
     * 测试用例：测试获取指定命名空间详情功能 - 正常场景
     * 场景：正常情况下，传入命名空间ID，返回对应的命名空间对象
     */
    @Test
    @Transactional
    public void testGetByNamespaceId_ShouldReturnNamespace() {
        Result<NamespaceDTO> result = namespaceService.getByNamespaceId(testNamespaceId);
        assertNotNull(result);
        NamespaceDTO namespaceBO = result.getData();
        assertEquals(testNamespaceId, namespaceBO.getNamespaceId());
        assertEquals(testNamespaceName, namespaceBO.getName());
    }

    /**
     * 测试用例：测试获取不存在的命名空间
     * 场景：传入不存在的命名空间ID，应该返回null或抛出异常
     */
    @Test
    @Transactional
    public void testGetByNamespaceId_WithNonExistentId_ShouldHandleGracefully() {
        String nonExistentId = "non-existent-id-" + UUID.randomUUID().toString();

        Result<NamespaceDTO> result = namespaceService.getByNamespaceId(nonExistentId);
        assertFalse("不存在的命名空间ID", result.getSuccess());
        assertEquals(NAMESPACE_NOT_EXIST.getCode(), result.getCode());
    }

    /**
     * 测试用例：测试更新命名空间功能 - 正常场景
     * 场景：正常情况下，传入更新后的命名空间对象，返回更新结果
     */
    @Test
    @Transactional
    public void testUpdate_ShouldReturnUpdateResult() {
        // 先获取原始数据
        Result<NamespaceDTO> original = namespaceService.getByNamespaceId(testNamespaceId);
        assertNotNull("测试前提：命名空间必须存在", original.getData());

        // 准备更新数据
        NamespaceUpdateDTO toUpdate = new NamespaceUpdateDTO();
        toUpdate.setNamespaceId(testNamespaceId);
        toUpdate.setDescription("Updated description " + System.currentTimeMillis());
        toUpdate.setOwners(Arrays.asList("149016", "149017"));

        namespaceService.update(toUpdate);

        // 验证更新结果
        Result<NamespaceDTO> updated = namespaceService.getByNamespaceId(testNamespaceId);
        assertEquals("描述应该已更新", toUpdate.getDescription(), updated.getData().getDescription());
        assertEquals("所有者列表应该已更新", toUpdate.getOwners().size(), updated.getData().getOwners().size());
    }

    /**
     * 测试用例：测试更新不存在的命名空间
     * 场景：更新一个不存在的命名空间，应该返回失败或抛出异常
     */
    @Test
    @Transactional
    public void testUpdate_WithNonExistentNamespace_ShouldFail() {
        NamespaceUpdateDTO nonExistent = new NamespaceUpdateDTO();
        nonExistent.setNamespaceId("non-existent-" + UUID.randomUUID().toString());
        nonExistent.setDescription("Non-existent Namespace");


        Result<Void> result = namespaceService.update(nonExistent);
        Assert.assertFalse("不存在的命名空间更新应该失败", result.isSuccess());
        Assert.assertEquals("不存在的命名空间更新应该失败", NAMESPACE_NOT_EXIST.getCode(), result.getCode());
    }

    /**
     * 测试用例：测试部分更新命名空间字段
     * 场景：只更新命名空间的部分字段，其他字段保持不变
     */
    @Test
    @Transactional
    public void testUpdate_WithPartialFields_ShouldOnlyUpdateSpecifiedFields() {
        // 先获取原始数据
        Result<NamespaceDTO> original = namespaceService.getByNamespaceId(testNamespaceId);
        assertNotNull("测试前提：命名空间必须存在", original.getData());

        // 只更新描述字段
        NamespaceUpdateDTO partialUpdate = new NamespaceUpdateDTO();
        partialUpdate.setNamespaceId(testNamespaceId);
        partialUpdate.setDescription("Partially updated description " + System.currentTimeMillis());

        namespaceService.update(partialUpdate);

        // 验证更新结果
        Result<NamespaceDTO> updated = namespaceService.getByNamespaceId(testNamespaceId);
        assertEquals("描述应该已更新", partialUpdate.getDescription(), updated.getData().getDescription());
        assertEquals("所有者列表应该保持不变", original.getData().getOwners().size(), updated.getData().getOwners().size());
    }
}
